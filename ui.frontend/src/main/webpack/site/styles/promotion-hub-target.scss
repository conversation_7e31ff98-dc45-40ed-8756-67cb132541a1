.tcb-promotion-hub-target {
  max-width: 120rem;
  margin: 0 auto;
  position: relative;
  z-index: 10;
  
  .display__text,
  .display__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 300;
    font-size: 1.5rem;
    line-height: 3rem;
    letter-spacing: 0;

    @include maxSm {
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 1.5rem;
      letter-spacing: -0.01625rem;
      vertical-align: middle;
    }
  }

  .offer-filter__dropdown {
    .dropdown__wrapper {
      margin-bottom: 0;
    }

    .dropdown__display {
      align-items: center;
      gap: 0.625rem;
      cursor: pointer;
      background-color: transparent;
      border: 0;
      padding: 0.625rem;
      justify-content: flex-start;

      @include maxSm {
        padding: 1.25rem 1rem;
      }
    }

    .display__text {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--cyan-blue);
      white-space: nowrap;

      @include maxSm {
        font-size: 1.25rem;
        font-weight: 600;
        line-height: 1.5rem;
      }
    }

    .dropdown__list {
      min-width: 15rem;
      max-width: 22.5rem;
      min-height: fit-content;

      overflow: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      @include maxSm {
        min-width: 10rem;
      }
    }

    .dropdown__item {
      font-size: 1.5rem;
      font-weight: 600;
      padding-left: 0.625rem;

      @include maxSm {
        font-size: 1.25rem;
        font-weight: 600;
        line-height: 1.5rem;
      }
    }

    .dropdown__item:hover {
      color: var(--cyan-blue);
    }
  }
}

.dropdown__item--hidden {
  display: none !important;
}
