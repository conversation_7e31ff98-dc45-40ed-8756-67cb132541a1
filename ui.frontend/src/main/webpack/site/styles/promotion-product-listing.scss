:root {
  --primary-color-gray-500: #e3e4e5;
}

.tcb-promotion-product-list {
  margin: 0 auto;
  max-width: 90rem;
  .promotion-product-listing__header {
    margin-bottom: 2rem;

    @include maxSm {
      margin-bottom: 1rem;
    }

  }

  .promotion-product-listing__see-detail-btn {
    a {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-top: 2rem;
      cursor: pointer;
      gap: 0.5rem;

      span {
        font-weight: bold;
      }
    }
  }

  .promotion-product-listing__tab {
    border-bottom: none;
    margin-bottom: 2rem;
    max-width: 100%;
    display: flex;
    align-items: center;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    overflow-x: scroll;
    gap: 0.75rem;

    &::-webkit-scrollbar {
      display: none;
    }

    .tab-card__item {
      .tab-card__link {
        white-space: nowrap;
        border: 0.063rem solid var(--primary-color-gray-500);
        padding: 0.5rem 0.75rem;
        border-radius: 2.063rem;
        background-color: var(--primary-white);
        font-weight: 400;
        color: var(--primary-black);
        font-size: 1rem;
        line-height: 1.5rem;
        letter-spacing: 0;
        cursor: pointer;
        box-sizing: border-box;
        height: 2.5rem;

        &.active {
          border-color: var(--primary-black);
          font-weight: 400;
          color: var(--primary-white);
          background-color: var(--primary-black);

          &::after {
            content: unset;
          }
        }
      }
    }
  }
}
