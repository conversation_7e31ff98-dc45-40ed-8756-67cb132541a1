.promotion-hub-banner {
  max-width: 120rem;
  height: 25rem;
  margin: 0 auto;
  position: relative;

  .promotion-banner-slick {
    width: 100%;
    height: 100%;

    .slick-list,
    .slick-track {
      height: 100%;
    }

    .slick-slide {
      > div:not(.promotion-slide) {
        height: 100%;
      }
    }

    .promotion-slide {
      width: 100%;
      height: 100%;
      position: relative;

      .tcb-promotion-slide_background {
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
      }

      .promotion-hub-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .promotion-slide-container {
        height: 100%;
        position: relative;
      }
    }

    .promotion-account {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .account-header {
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 1.3125rem;
        letter-spacing: 0.125rem;
        text-transform: uppercase;
        color: var(--primary-black);
      }

      .account-title > * {
        font-weight: 300;
        font-size: 2rem;
        line-height: 2.5rem;
        letter-spacing: 0;
        color: var(--primary-black);
      }
    }

    @include xs {
      .promotion-account {
        gap: 1rem;
      }
    }

    .promotion-hub-breadscrum {
      position: absolute;
      height: 1.3125rem;
      top: 1rem;
      gap: 0.5rem;
    }

    .promotion-hub-content {
      position: absolute;
      max-width: 47.71625rem;
      width: 100%;
      top: 9.125rem;
      gap: 1rem;
      display: flex;
      flex-direction: column;

      .promotion-detail-link {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        span {
          font-weight: 600;
          font-size: 1rem;
          line-height: 1.5rem;
          letter-spacing: 0;
          vertical-align: middle;
          color: var(--body);
        }

        img {
          width: 1rem;
          height: 1rem;
        }
      }
    }

    @include xs {
      .promotion-hub-breadscrum {
        display: none;
      }

      .promotion-hub-content {
        top: 17.0625rem;
        padding: 1.5rem 1rem 3rem 1rem;
        gap: 1rem;
        left: 0;
        width: 20.5rem;

        .account-title {
          font-size: 1.5rem;
          font-weight: 600;
          line-height: 2.25rem;
        }
      }
    }

    .slick-dots {
      position: absolute;
      width: 100%;
      gap: 0.5rem;
      display: flex;
      justify-content: center;
      bottom: 1rem;
      list-style: none;

      li {
        margin: 0 0.25rem;

        button {
          width: 0.5rem;
          height: 0.5rem;
          border-radius: 50%;
          background: #ccc;
          border: none;
          text-indent: -624.9375rem;
        }

        &.slick-active button {
          background: var(--accent);
        }
      }
    }

    @include xs {
      .slick-dots {
        left: 0;
        bottom: 0;
      }
    }
  }
}

@include xs {
  .promotion-hub-banner {
    height: 29.375rem;
  }
}
