import 'slick-carousel';
import { BaseComponent } from './base';

export class TcbPromotionBannerCarousel extends BaseComponent {
  constructor() {
    super();
    this.autoPlaySpeed = this.dataset.autoplaySpeed || false;
  }

  connectedCallback() {
    this.initCarousel();
  }

  initCarousel() {
    $(this.querySelector('.promotion-banner-slick')).slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: this.autoPlaySpeed,
      dots: true,
      arrows: false,
      customPaging: (slider, i) => (i < 4 ? `<button>${i + 1}</button>` : ''),
    });
  }
}
