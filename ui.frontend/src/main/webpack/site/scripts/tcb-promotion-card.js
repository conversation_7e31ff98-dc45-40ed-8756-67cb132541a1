import { BaseComponent } from './base';
import { renderDayExpired } from './utils/day-expired.util';
import { renderImage } from './utils/default-image.util';
import { scrollText } from './exclusive-helper';
import {
  SELECTOR_DETAIL_LINK,
  LABEL_PDF_PREVIEW,
  LABEL_PDF_DOWNLOAD,
  LABEL_PDF_LOADING_MESSAGE,
} from './constants/offer-common';

export class TcbPromotionCard extends BaseComponent {
  selectorPdfLink = `.card a.tcb-card__link[href]:not(${SELECTOR_DETAIL_LINK})`;

  constructor() {
    super();
    this.intervalId = null;
    this.renderInterval = 1000;
    this.previewPdf = {
      downloadPdfLabel: this.dataset.previewDownloadLabel || LABEL_PDF_DOWNLOAD,
      pdfTitle: this.dataset.defaultTitle || LABEL_PDF_PREVIEW,
      loadingPdf: this.dataset.previewLoadingLabel || LABEL_PDF_LOADING_MESSAGE,
    };
    if (!this.querySelector('.popup-download')) {
      const popup = document.createElement('div');
      popup.className = 'popup-download';
      this.appendChild(popup);
    }
    this.init();
  }

  init() {
    renderDayExpired();
    renderImage();
    scrollText();

    this.handlePdfClick();

    this.intervalId = setInterval(() => {
      renderDayExpired();
    }, this.renderInterval);
  }

  destroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  showPreviewModalListener(event) {
    const target = event.currentTarget;
    const dataDownloadPdfLabel = this.previewPdf.downloadPdfLabel;
    const previewURL = $(target).attr('href');
    const previewTitle = this.previewPdf.pdfTitle;
    const previewLoadingLabel = this.previewPdf.loadingPdf;

    if (previewURL && previewURL.includes('.pdf')) {
      event.preventDefault();
      this.triggerShowPreviewModal(previewURL, previewTitle, previewLoadingLabel, dataDownloadPdfLabel);
    }
  }

  triggerShowPreviewModal(url, title, loadingLabel, downloadLabel) {
    const previewModalElement = $(this).find('.popup-download');
    previewModalElement.trigger('show-preview-modal', [
      {
        url,
        documentType: 'pdf',
        title,
        loadingLabel,
        downloadLabel,
      },
    ]);
  }

  handlePdfClick() {
    $(this).off('click', this.selectorPdfLink);
    $(this).on('click', this.selectorPdfLink, this.showPreviewModalListener.bind(this));
  }
}
