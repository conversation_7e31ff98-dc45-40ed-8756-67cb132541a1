import { BaseComponent } from './base';
import { renderDayExpired } from './utils/day-expired.util';
import { renderImage } from './utils/default-image.util';
import { filterCardsByTab, handleClickPdf, scrollText, startDayExpiredInterval } from './exclusive-helper';

export class ExclusiveOffer extends BaseComponent {

  constructor() {
    super();
    this.init();
  }

  init() {
    $(document).ready(() => {
      renderDayExpired();
      startDayExpiredInterval();
      filterCardsByTab();
      renderImage();
      handleClickPdf();
      scrollText();
    });
  }
}
