import { getDataResultPage } from '../scripts/utils/get-data-result-page.util';
import { getEndpoint } from './utils/promotion-generate-endpoint.util';
import { LocationUtil } from './utils/location.util';
import { StorageUtils } from './utils/storage.util';
import { setURLParams, removeURLParams, getQueryParam } from './utils/params.util';
import { BaseComponent } from './base';
import {
  ID_PROMOTION_FILTER,
  ID_PROMOTION_SEARCH,
  CLASS_HIDDEN_ELEMENT,
  KEY_RECENT_SEARCH,
} from './constants/offer-common';
import DOMPurify from 'dompurify';

export class TcbResultPage extends BaseComponent {
  constructor() {
    super();
    this.root = null;
    this.DEFAULT_SORT_PARAMS = 'latest';
    this.paramUrl = '';
    this.textInputSearch = '';
    this.numberResultMore = 0;
    this.maxItemsRecent = 10;

    this.paramsName = {
      detail: 'detail',
      detailName: 'name',
      cardTypes: 'card-types',
      sort: 'sort',
      products: 'products',
      memberships: 'memberships',
      types: 'types',
      partner: 'partner',
      limit: 'limit',
      offset: 'offset',
      searchText: 'q',
      location: 'location',
      membership: 'membership',
    };

    this.paramsValue = {
      limit: 6,
      offset: 0,
      sort: this.DEFAULT_SORT_PARAMS,
      products: '',
      memberships: '',
      types: '',
      searchText: '',
      location: '',
      cardTypes: '',
    };

    this.url = new URL(window.location.href);

    const dataset = this.dataset || {};
    this.dataExpiryDate = {
      lang: dataset.lang,
      dayText: dataset.dayText,
      labelExpired: dataset.labelExpired,
      labelExpiredCountDown: dataset.labelExpiredCountDown,
    };

    this.isCardlabel = dataset.cardLabel === 'true';
    this.dataUrl = dataset.url;
    this.nameIdAllPage = dataset.idDisplayPage;
    this.classGroupCardName = '';
    this.nameAddMore = dataset.readMore;
    this.searchCountElement = dataset.searchCount;
    this.idRoot = dataset.idRoot;
  }

  connectedCallback() {
    this.root = document.getElementById(this.id);
    if (!this.root) {
      return;
    }
    this.initElements();
    this.getParamUrl();
    this.bindEvents();

    if (this.id === ID_PROMOTION_FILTER) {
      this.handleParamFilter();
      this.handleSort();
    }
  }

  initElements() {
    this.resultNotfound = this.root.querySelector('.tcb-result-page-hero-notfound') || null;
    if (this.id === ID_PROMOTION_FILTER) {
      this.resultPage = this.root.querySelector('.tcb-result-page-content tcb-promotion-card') || null;
    } else {
      this.resultPage = this.root.querySelector('.tcb-result-page-content') || null;
    }
    this.countPromotion = this.root.querySelector('.promotion-total-count') || null;
    this.addKeyWork = this.root.querySelector('.keywork-notfoud') || null;
    this.countMorePage = this.root.querySelector('.add-more-text') || null;
    this.addMoreResult = this.root.querySelector('.button-more-promotion') || null;
    this.selectSortFilter = this.root.querySelector('.offer-filter__dropdown') || null;
    this.notfoundItems = this.root.querySelectorAll('.notfound-input-dropdown-item') || null;

    if (this.nameIdAllPage) {
      this.toggleAllPage = document.querySelector('#' + this.nameIdAllPage) || null;
    }

    this.toggleResultPage = document.querySelector('.display-result-page') || null;
  }

  getParamUrl() {
    this.paramUrl = LocationUtil.getPageUrlParams();
  }

  bindEvents() {
    if (this.addMoreResult) {
      this.addMoreResult.addEventListener('click', () => {
        this.paramsValue.offset += this.paramsValue.limit;
        this.getPromotions();
      });
    }

    if (this.selectSortFilter) {
      this.selectSortFilter.addEventListener('click', this.handleSortItemClick.bind(this));
    }

    if (this.notfoundItems) {
      this.notfoundItems.forEach((item) => {
        item.addEventListener('click', this.handleNotfoundItemClick.bind(this));
      });
    }
  }

  handleSortItemClick(e) {
    const target = e.target.closest('.dropdown__item');
    if (!target) {
      return;
    }

    const valueSort = target.getAttribute('value');

    if (valueSort === 'most-popular') {
      removeURLParams(this.paramsName.sort, true);
      this.paramsValue.sort = this.DEFAULT_SORT_PARAMS;
    } else {
      let sortValue = '';
      if (valueSort) {
        sortValue = valueSort.split('-').join(' ');
      }
      setURLParams(this.paramsName.sort, sortValue, true);
      this.paramsValue.sort = '';
    }
    this.handleParamFilter();
  }

  handleValueSearch() {
    const nameElementGroupCard = '#' + ID_PROMOTION_SEARCH + ' .group-card';
    const groupCardContent = document.querySelector(nameElementGroupCard);
    if (groupCardContent) {
      groupCardContent.innerHTML = DOMPurify.sanitize('');
    }

    this.classGroupCardName = nameElementGroupCard;
    this.paramsValue.offset = 0;

    const valueParamSearch = getQueryParam('q');
    if (valueParamSearch) {
      this.textInputSearch = valueParamSearch;
    } else {
      this.textInputSearch = '';
    }

    if (this.addKeyWork) {
      this.addKeyWork.innerHTML = DOMPurify.sanitize(this.textInputSearch);
    }

    this.handleDisplayPageResult();
    if (this.textInputSearch === '') {
      this.hideResultPage();
      return;
    }
    this.handleParamUrl();
  }

  handleDisplayPageResult() {
    const isEmptySearch = this.textInputSearch === '';

    if (isEmptySearch) {
      if (this.toggleResultPage) {
        this.toggleResultPage.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      if (this.toggleAllPage) {
        this.toggleAllPage.classList.remove(CLASS_HIDDEN_ELEMENT);
      }
    } else {
      if (this.toggleAllPage) {
        this.toggleAllPage.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      if (this.toggleResultPage) {
        this.toggleResultPage.classList.remove(CLASS_HIDDEN_ELEMENT);
      }
    }
  }

  handleParamFilter() {
    const nameElementGroupCard = '#' + ID_PROMOTION_FILTER + ' .group-card';
    const groupCardContent = document.querySelector(nameElementGroupCard);
    if (groupCardContent) {
      groupCardContent.innerHTML = '';
    }

    this.classGroupCardName = nameElementGroupCard;
    this.paramsValue.offset = 0;
    this.handleParamUrl();
  }

  handleParamUrl() {
    this.hideResultPage();
    this.handleTogglePageResult();
    this.getPromotions();
  }

  handleTogglePageResult() {
    if (this.resultNotfound) {
      this.resultNotfound.classList.add(CLASS_HIDDEN_ELEMENT);
    }
  }

  getPromotions() {
    getDataResultPage({
      dataUrl: getEndpoint(this.dataUrl, {
        limit: this.paramsValue.limit,
        offset: this.paramsValue.offset || 0,
        sort: this.paramsValue.sort,
      }),
      classGroupCardName: this.classGroupCardName,
      dataExpiryDate: this.dataExpiryDate,
      isCardlabel: this.isCardlabel,
      totalPromotionData: (total) => {
        this.handleCountMore(total);
      },
    });
  }

  handleCountMore(total) {
    if (total === 0) {
      this.renderTcbCardNotFound();
    } else {
      if (this.resultPage) {
        this.resultPage.classList.remove(CLASS_HIDDEN_ELEMENT);
      }
      this.setRecentSearchLocal();
    }

    if (this.countPromotion) {
      const countElement = total + ' ' + this.searchCountElement;
      this.countPromotion.innerHTML = DOMPurify.sanitize(countElement);
    }

    if (this.addMoreResult && this.addMoreResult.parentElement) {
      if (total <= this.paramsValue.limit) {
        this.addMoreResult.parentElement.classList.add(CLASS_HIDDEN_ELEMENT);
      } else {
        this.addMoreResult.parentElement.classList.remove(CLASS_HIDDEN_ELEMENT);
      }
    }

    const remaining = total - this.paramsValue.offset - this.paramsValue.limit;
    this.numberResultMore = 0;
    if (remaining > 0) {
      this.numberResultMore = Math.min(this.paramsValue.limit, remaining);
    }

    if (this.numberResultMore > 0) {
      if (this.countMorePage) {
        this.countMorePage.innerHTML = DOMPurify.sanitize(this.nameAddMore);
      }
    } else {
      if (this.addMoreResult && this.addMoreResult.parentElement) {
        this.addMoreResult.parentElement.classList.add(CLASS_HIDDEN_ELEMENT);
      }
    }
  }

  renderTcbCardNotFound() {
    if (this.resultNotfound) {
      this.resultNotfound.classList.remove(CLASS_HIDDEN_ELEMENT);
    }
    this.hideResultPage();
  }

  hideResultPage() {
    if (this.resultPage) {
      this.resultPage.classList.add(CLASS_HIDDEN_ELEMENT);
    }
  }

  setRecentSearchLocal() {
    const value = (this.textInputSearch || '').trim();
    if (value === '') {
      return;
    }

    let recentSearch = [];
    try {
      const stored = StorageUtils.get(KEY_RECENT_SEARCH);
      if (stored) {
        recentSearch = stored;
      }
    } catch {}

    if (!Array.isArray(recentSearch)) {
      recentSearch = [];
    }

    recentSearch = recentSearch.filter(function (item) {
      return item !== value;
    });

    recentSearch.unshift(value);

    if (recentSearch.length > this.maxItemsRecent) {
      recentSearch = recentSearch.slice(0, this.maxItemsRecent);
    }

    StorageUtils.set(KEY_RECENT_SEARCH, recentSearch);

    const needPromotionSearch = document.querySelector('tcb-need-promotion-search');
    if (needPromotionSearch && typeof needPromotionSearch.renderRecentSearchFromLocalStorage === 'function') {
      needPromotionSearch.renderRecentSearchFromLocalStorage();
    }
  }

  handleSort() {
    const paramsObject = LocationUtil.getUrlParamObj();
    let sortParam = paramsObject.sort;
    let sortItem;

    if (sortParam) {
      sortParam = sortParam.replace(/\+/g, '-');
      sortItem = this.selectSortFilter.querySelector('li[value="' + sortParam + '"]');
    } else {
      if (this.selectSortFilter){
        sortItem = this.selectSortFilter.querySelector('li[value="most-popular"]');
      }
    }

    if (sortItem) {
      const sortText = sortItem.getAttribute('data-sort');
      const displayText = this.selectSortFilter.querySelector('.display__text');

      if (displayText) {
        displayText.textContent = sortText;
      }
    }
  }

  handleNotfoundItemClick(e) {
    const valueItem = e.target.textContent;
    const tcbSearch = document.querySelector('tcb-need-promotion-search');
    tcbSearch.elementInputSearch.value = valueItem;
    tcbSearch.buttonSearch.dispatchEvent(tcbSearch.createEvent('click'));
  }
}
